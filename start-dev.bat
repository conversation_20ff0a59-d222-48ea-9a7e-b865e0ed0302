@echo off
echo Starting Firebase Emulators and React Development Server...
echo.

REM Check if Firebase CLI is available
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Firebase CLI not found. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

REM Start Firebase emulators in background
echo Starting Firebase emulators...
start "Firebase Emulators" cmd /k "firebase emulators:start"

REM Wait a moment for emulators to start
timeout /t 5 /nobreak >nul

REM Start React development server
echo Starting React development server...
cd cyber-pos-system
start "React Dev Server" cmd /k "npm start"

echo.
echo Development environment started!
echo.
echo Firebase Emulator UI: http://localhost:4000
echo React App: http://localhost:3000
echo.
echo Press any key to exit...
pause >nul
