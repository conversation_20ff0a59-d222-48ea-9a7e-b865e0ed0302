import { auth, db, storage } from '../config/firebase';
import { 
  signInAnonymously, 
  signOut 
} from 'firebase/auth';
import { 
  collection, 
  addDoc, 
  getDocs, 
  deleteDoc,
  doc 
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';

/**
 * Test Firebase connection and basic functionality
 * This function tests Auth, Firestore, and Storage
 */
export const testFirebaseConnection = async (): Promise<{
  success: boolean;
  results: {
    auth: boolean;
    firestore: boolean;
    storage: boolean;
  };
  errors: string[];
}> => {
  const results = {
    auth: false,
    firestore: false,
    storage: false
  };
  const errors: string[] = [];

  try {
    // Test Authentication
    console.log('🔐 Testing Firebase Authentication...');
    const userCredential = await signInAnonymously(auth);
    if (userCredential.user) {
      results.auth = true;
      console.log('✅ Authentication test passed');
      
      // Sign out after test
      await signOut(auth);
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    errors.push(`Auth: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  try {
    // Test Firestore
    console.log('🗄️ Testing Firestore Database...');
    const testCollection = collection(db, 'test');
    const testDoc = await addDoc(testCollection, {
      message: 'Firebase test document',
      timestamp: new Date(),
      testId: Math.random().toString(36).substr(2, 9)
    });
    
    // Read the document back
    const snapshot = await getDocs(testCollection);
    if (!snapshot.empty) {
      results.firestore = true;
      console.log('✅ Firestore test passed');
      
      // Clean up test document
      await deleteDoc(testDoc);
    }
  } catch (error) {
    console.error('❌ Firestore test failed:', error);
    errors.push(`Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  try {
    // Test Storage
    console.log('📁 Testing Firebase Storage...');
    const testFile = new Blob(['Hello Firebase Storage!'], { type: 'text/plain' });
    const testRef = ref(storage, `test/test-${Date.now()}.txt`);
    
    const uploadResult = await uploadBytes(testRef, testFile);
    if (uploadResult) {
      const downloadURL = await getDownloadURL(testRef);
      if (downloadURL) {
        results.storage = true;
        console.log('✅ Storage test passed');
        
        // Clean up test file
        await deleteObject(testRef);
      }
    }
  } catch (error) {
    console.error('❌ Storage test failed:', error);
    errors.push(`Storage: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  const success = results.auth && results.firestore && results.storage;
  
  console.log('🔥 Firebase Test Results:', {
    success,
    results,
    errors: errors.length > 0 ? errors : ['No errors']
  });

  return { success, results, errors };
};

/**
 * Check if Firebase emulators are being used
 */
export const isUsingEmulators = (): boolean => {
  return process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true';
};

/**
 * Get Firebase configuration info
 */
export const getFirebaseInfo = () => {
  return {
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    environment: process.env.REACT_APP_ENVIRONMENT,
    usingEmulators: isUsingEmulators(),
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN
  };
};
