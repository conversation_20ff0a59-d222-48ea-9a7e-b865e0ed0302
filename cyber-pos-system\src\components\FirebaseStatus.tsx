import React, { useState, useEffect } from 'react';
import { testFirebaseConnection, getFirebaseInfo, isUsingEmulators } from '../utils/firebaseTest';

interface FirebaseStatusProps {
  showDetails?: boolean;
}

const FirebaseStatus: React.FC<FirebaseStatusProps> = ({ showDetails = false }) => {
  const [status, setStatus] = useState<{
    loading: boolean;
    success: boolean;
    results: {
      auth: boolean;
      firestore: boolean;
      storage: boolean;
    };
    errors: string[];
  }>({
    loading: true,
    success: false,
    results: { auth: false, firestore: false, storage: false },
    errors: []
  });

  const firebaseInfo = getFirebaseInfo();

  useEffect(() => {
    const runTest = async () => {
      setStatus(prev => ({ ...prev, loading: true }));
      try {
        const result = await testFirebaseConnection();
        setStatus({
          loading: false,
          success: result.success,
          results: result.results,
          errors: result.errors
        });
      } catch (error) {
        setStatus({
          loading: false,
          success: false,
          results: { auth: false, firestore: false, storage: false },
          errors: [error instanceof Error ? error.message : 'Unknown error']
        });
      }
    };

    runTest();
  }, []);

  const getStatusIcon = (success: boolean) => {
    return success ? '✅' : '❌';
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-600' : 'text-red-600';
  };

  if (!showDetails) {
    return (
      <div className="flex items-center space-x-2 text-sm">
        <span className={`font-medium ${getStatusColor(status.success)}`}>
          Firebase: {status.loading ? '⏳' : getStatusIcon(status.success)}
        </span>
        {isUsingEmulators() && (
          <span className="text-blue-600 text-xs bg-blue-100 px-2 py-1 rounded">
            Emulator
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h3 className="text-lg font-semibold mb-3">Firebase Connection Status</h3>
      
      {/* Overall Status */}
      <div className="mb-4">
        <div className={`text-lg font-medium ${getStatusColor(status.success)}`}>
          {status.loading ? '⏳ Testing connection...' : 
           status.success ? '✅ All services connected' : '❌ Connection issues detected'}
        </div>
      </div>

      {/* Service Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <span className={getStatusColor(status.results.auth)}>
            {getStatusIcon(status.results.auth)}
          </span>
          <span>Authentication</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={getStatusColor(status.results.firestore)}>
            {getStatusIcon(status.results.firestore)}
          </span>
          <span>Firestore</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={getStatusColor(status.results.storage)}>
            {getStatusIcon(status.results.storage)}
          </span>
          <span>Storage</span>
        </div>
      </div>

      {/* Configuration Info */}
      <div className="bg-gray-50 p-3 rounded text-sm">
        <h4 className="font-medium mb-2">Configuration</h4>
        <div className="space-y-1">
          <div>Project ID: <code className="bg-gray-200 px-1 rounded">{firebaseInfo.projectId}</code></div>
          <div>Environment: <code className="bg-gray-200 px-1 rounded">{firebaseInfo.environment}</code></div>
          <div>Using Emulators: <code className="bg-gray-200 px-1 rounded">{firebaseInfo.usingEmulators ? 'Yes' : 'No'}</code></div>
        </div>
      </div>

      {/* Errors */}
      {status.errors.length > 0 && (
        <div className="mt-4 bg-red-50 p-3 rounded">
          <h4 className="font-medium text-red-800 mb-2">Errors</h4>
          <ul className="text-sm text-red-700 space-y-1">
            {status.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Retry Button */}
      <div className="mt-4">
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          disabled={status.loading}
        >
          {status.loading ? 'Testing...' : 'Retry Test'}
        </button>
      </div>
    </div>
  );
};

export default FirebaseStatus;
