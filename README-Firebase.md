# 🔥 Firebase Setup for Cyber POS System

## 📋 Overview

Your Cyber POS system is now configured with Firebase for:
- **Authentication** - User login and role management
- **Firestore** - Real-time database for products, sales, inventory
- **Storage** - File uploads (receipts, product images)
- **Hosting** - Web app deployment
- **Emulators** - Local development environment

## 🚀 Quick Start

### Option 1: Use the Start Scripts (Recommended)

**Windows:**
```bash
# Double-click or run in terminal
start-dev.bat
```

**Mac/Linux:**
```bash
# Make executable and run
chmod +x start-dev.sh
./start-dev.sh
```

### Option 2: Manual Start

```bash
# Terminal 1: Start Firebase emulators
firebase emulators:start

# Terminal 2: Start React app
cd cyber-pos-system
npm start
```

## 🔧 Configuration Files Created

- `firebase.json` - Firebase project configuration
- `firestore.rules` - Database security rules
- `storage.rules` - File storage security rules
- `firestore.indexes.json` - Database indexes
- `.firebaserc` - Project aliases
- `cyber-pos-system/.env.local` - Environment variables

## 📁 Project Structure

```
Cyber POS/
├── firebase.json              # Firebase configuration
├── firestore.rules           # Database security rules
├── storage.rules             # Storage security rules
├── firestore.indexes.json    # Database indexes
├── .firebaserc              # Project settings
├── start-dev.bat            # Windows start script
├── start-dev.sh             # Unix start script
└── cyber-pos-system/
    ├── .env.local           # Environment variables
    ├── .env.example         # Environment template
    └── src/
        ├── config/
        │   └── firebase.ts  # Firebase initialization
        ├── components/
        │   └── FirebaseStatus.tsx  # Connection status
        └── utils/
            └── firebaseTest.ts     # Connection testing
```

## 🛠️ Next Steps

### 1. Create Firebase Project (Required for Production)

1. Visit [Firebase Console](https://console.firebase.google.com/)
2. Create new project: "cyber-pos-system"
3. Enable Authentication, Firestore, Storage, Hosting
4. Get your config object and update `.env.local`

### 2. Initialize Firebase CLI

```bash
# Login to Firebase
firebase login

# Initialize project (run in project root)
firebase init

# Select: Firestore, Storage, Hosting, Emulators
# Choose existing project
# Accept defaults for most options
```

### 3. Update Environment Variables

Edit `cyber-pos-system/.env.local` with your real Firebase config:

```env
REACT_APP_FIREBASE_API_KEY=your_actual_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
# ... etc
```

### 4. Deploy Security Rules

```bash
firebase deploy --only firestore:rules,storage:rules
```

### 5. Build and Deploy

```bash
# Build the app
cd cyber-pos-system
npm run build

# Deploy to Firebase Hosting
cd ..
firebase deploy --only hosting
```

## 🔒 Security Rules

### Firestore Rules
- Users can only access their own data
- Admin/Technician roles can manage products/inventory
- All authenticated users can create sales

### Storage Rules
- Product images: Admin/Technician upload, all read
- User avatars: Own avatar only
- Receipts: All authenticated users

## 🧪 Testing Firebase Connection

The app includes a Firebase connection test utility:

```typescript
import { testFirebaseConnection } from './utils/firebaseTest';

// Test all Firebase services
const result = await testFirebaseConnection();
console.log(result);
```

Or use the `FirebaseStatus` component to show connection status in your UI.

## 📱 Available NPM Scripts

```bash
# Start with emulators
npm run dev:with-emulators

# Deploy everything
npm run firebase:deploy

# Deploy only hosting
npm run firebase:deploy:hosting

# Deploy only rules
npm run firebase:deploy:rules

# Start emulators only
npm run firebase:emulators
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Change emulator ports in `firebase.json`
2. **Permission errors**: Check security rules
3. **Connection timeouts**: Ensure emulators are running
4. **Build errors**: Verify environment variables

### Useful Commands

```bash
# Check Firebase status
firebase --version
firebase projects:list

# View logs
firebase emulators:start --debug

# Reset emulator data
firebase emulators:start --import=./emulator-data --export-on-exit
```

## 🌐 URLs (Development)

- **React App**: http://localhost:3000
- **Firebase Emulator UI**: http://localhost:4000
- **Firestore Emulator**: http://localhost:8080
- **Auth Emulator**: http://localhost:9099
- **Storage Emulator**: http://localhost:9199

## 📚 Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite)
- [React Firebase Hooks](https://github.com/CSFrequency/react-firebase-hooks)

## 🎯 Production Checklist

- [ ] Create production Firebase project
- [ ] Update environment variables
- [ ] Deploy security rules
- [ ] Set up CI/CD pipeline
- [ ] Configure custom domain
- [ ] Enable monitoring and analytics
- [ ] Set up backup strategy
