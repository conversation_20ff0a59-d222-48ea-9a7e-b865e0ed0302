# Firebase Configuration for Development
# This file is for local development with emulators

# Firebase Project Configuration (using demo values for emulator)
REACT_APP_FIREBASE_API_KEY=ba0bf70db7d90b9374430cf2be54ca8411c5c340
REACT_APP_FIREBASE_AUTH_DOMAIN=<EMAIL>
REACT_APP_FIREBASE_PROJECT_ID=cyber-pos-system
REACT_APP_FIREBASE_STORAGE_BUCKET=demo-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=cyber-pos-system

# Environment
REACT_APP_ENVIRONMENT=development

# Firebase Emulator Configuration
REACT_APP_USE_FIREBASE_EMULATOR=true
REACT_APP_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
REACT_APP_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
REACT_APP_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# Note: When you create your real Firebase project, replace the demo values above
# with your actual Firebase configuration values and set REACT_APP_USE_FIREBASE_EMULATOR=false
# for production builds
