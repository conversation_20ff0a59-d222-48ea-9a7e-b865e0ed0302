#!/bin/bash

echo "Starting Firebase Emulators and React Development Server..."
echo

# Check if Firebase CLI is available
if ! command -v firebase &> /dev/null; then
    echo "Error: Firebase CLI not found. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo "Stopping development servers..."
    kill $FIREBASE_PID $REACT_PID 2>/dev/null
    exit 0
}

# Set up trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start Firebase emulators in background
echo "Starting Firebase emulators..."
firebase emulators:start &
FIREBASE_PID=$!

# Wait a moment for emulators to start
sleep 5

# Start React development server in background
echo "Starting React development server..."
cd cyber-pos-system
npm start &
REACT_PID=$!

echo
echo "Development environment started!"
echo
echo "Firebase Emulator UI: http://localhost:4000"
echo "React App: http://localhost:3000"
echo
echo "Press Ctrl+C to stop all servers..."

# Wait for background processes
wait
